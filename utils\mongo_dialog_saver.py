from utils.mongo_client import get_mongo_client
import os

async def save_dialog_to_mongo(session_id, user_id, duration, score, agent_performance):

    db = await get_mongo_client()
    collection = db["dialog"]
    doc = {
        "session_id": session_id,
        "user_id": user_id,
        "duration": duration,
        "score": score,
        "agent_performance": agent_performance or ""
    }
    result = await collection.insert_one(doc)
    return str(result.inserted_id) 
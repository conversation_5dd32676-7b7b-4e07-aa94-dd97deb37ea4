# 🎙️ AI Voice Agents Platform - Architecture Documentation

> **A comprehensive guide to the Voice Agents Platform architecture, data flow, and agent responsibilities**

---

## 📋 Core Components Overview

The Voice Agents Platform is built on a modular architecture where specialized agents handle different aspects of voice interaction processing. Each agent inherits from `BaseAgent` and follows standardized patterns for logging, error handling, Redis integration, and pub/sub communication.

### 🏗️ System Architecture

```mermaid
flowchart TD
    subgraph User["👤 User Interface"]
        U1["🎤 Audio Input"]
        U2["🔊 Audio Output"]
    end

    subgraph Orchestrator["🎯 Orchestrator"]
        O1["Session Management"]
        O2["Agent Coordination"]
        O3["Pub/Sub Listener"]
    end

    subgraph Agents["🤖 Processing Agents"]
        A1["STTAgent<br/>Speech → Text"]
        A2["PreprocessingAgent<br/>Text Analysis"]
        A3["ProcessingAgent<br/>Business Logic"]
        A4["TTSAgent<br/>Text → Speech"]
        A5["FillerTTSAgent<br/>Filler Audio"]
    end

    subgraph Storage["💾 Data Layer"]
        R1["Redis Context Store<br/>(Contextual Memory)"]
        R2["Redis Pub/Sub<br/>(Notifications)"]
        R3["Persistent Memory<br/>(Long-term Storage)"]
    end

    %% Data Flow
    U1 --> O1
    O1 --> A1
    A1 --> R1
    A1 --> R2
    R2 --> O2
    O2 --> A2
    A2 --> R1
    A2 --> R2
    R2 --> O2
    O2 --> A3
    A3 --> R1
    A3 --> R3
    A3 --> R2
    R2 --> O2
    O2 --> A4
    A4 --> R1
    A4 --> R2
    R2 --> O2
    O2 --> U2
    
    %% Filler audio can be triggered anytime
    A5 -.-> R2
    A5 -.-> U2
```

---

## 🔄 Complete Data Flow Sequence

### 1️⃣ **User Input Processing**
```
User speaks → Audio captured → Sent to Orchestrator
```

### 2️⃣ **Speech-to-Text Pipeline**
```
Orchestrator → STTAgent → OpenAI Whisper → Text transcript
→ Save to Redis context → Publish completion notification
```

### 3️⃣ **Text Preprocessing Pipeline**
```
Orchestrator receives STT completion → PreprocessingAgent
→ Load transcript from Redis → Clean text + Detect intent/emotion/gender
→ Update Redis context → Publish completion notification
```

### 4️⃣ **Business Logic Processing**
```
Orchestrator receives preprocessing completion → ProcessingAgent
→ Load context from Redis → Access conversation + persistent memory
→ Route to internal/database/RAG logic → Generate LLM response
→ Save conversation turn → Update Redis context → Publish completion
```

### 5️⃣ **Text-to-Speech Pipeline**
```
Orchestrator receives processing completion → TTSAgent
→ Load response from Redis → Select voice based on gender/emotion
→ Generate audio file → Save audio path to Redis → Publish completion
```

### 6️⃣ **Audio Delivery**
```
Orchestrator receives TTS completion → Retrieve audio path from Redis
→ Stream audio to user interface
```

---

## 🤖 Agent Deep Dive

### 🎯 **BaseAgent** - The Foundation

**Core Responsibilities:**
- **Structured Logging**: Every action logged with context, metrics, and session tracking
- **Error Handling**: Standardized error catching, logging, and fallback responses
- **Redis Integration**: Context saving/loading, pub/sub messaging
- **A2A Communication**: Agent-to-agent messaging via Redis channels
- **Memory Management**: Conversation history and persistent data access

**Key Methods:**
- `safe_process()`: Wraps all processing with error handling and timing
- `save_context()` / `load_context()`: Redis context management
- `publish_notification()`: Pub/sub event publishing
- `handle_redis_fallback()`: Fallback mechanism when Redis is unavailable

---

### 🎤 **STTAgent** - Speech Recognition

**What it does internally:**

1. **Audio Processing**
   - Accepts audio as bytes or file path
   - Creates temporary files for byte data
   - Handles multiple audio formats

2. **OpenAI Whisper Integration**
   - Configures language and response format from context
   - Processes audio through Whisper API with retry logic
   - Extracts text transcript from response

3. **Redis Context Management**
   - Loads latest shared context from Redis
   - Saves transcript and processing latency
   - Updates context keys: `transcript`, `latencySTT`

4. **Orchestrator Communication**
   - Publishes completion notification to `agent_completion` channel
   - Includes status and updated context keys
   - Handles error notifications with fallback audio generation

5. **Fallback Handling**
   - Generates fallback audio on errors
   - Returns standardized error responses
   - Cleans up temporary files

**Redis Keys Updated:** `transcript`, `latencySTT`

---

### 🧠 **PreprocessingAgent** - Text Analysis

**What it does internally:**

1. **Context Loading**
   - Loads shared context from Redis using session ID
   - Retrieves transcript from STTAgent processing
   - Handles Redis fallback scenarios

2. **Text Processing Pipeline**
   - **Text Cleaning**: Normalizes whitespace and casing
   - **Intent Classification**: Uses GPT-4o-mini to identify user intent
   - **Emotion Detection**: Analyzes emotional tone (happy, sad, neutral, etc.)
   - **Gender Detection**: Identifies speaker gender for voice selection

3. **LLM Integration**
   - Configurable emotion/gender detection via config file
   - Retry logic for OpenAI API calls
   - Fallback to defaults on LLM failures

4. **Contextual Memory Updates**
   - Saves processed data to Redis context
   - Updates keys: `intent`, `clean_text`, `emotion`, `gender`, `latencyPreprocessing`
   - Reloads latest context before saving to prevent overwrites

5. **Notification System**
   - Publishes completion status to orchestrator
   - Includes list of updated context keys
   - Handles error notifications with fallback responses

**Redis Keys Updated:** `intent`, `clean_text`, `emotion`, `gender`, `latencyPreprocessing`

---

### ⚙️ **ProcessingAgent** - Business Logic Engine

**What it does internally:**

1. **Memory Access**
   - **Contextual Memory**: Recent conversation history via MemoryManager
   - **Persistent Memory**: Long-term user data and preferences
   - Loads clean text and intent from preprocessing stage

2. **Routing Logic**
   - **Internal Logic**: Account information, basic queries
   - **Database Routing**: Complex data retrieval operations  
   - **RAG Engine**: Knowledge base and document queries
   - Routes based on keywords and intent analysis

3. **LLM Post-Processing**
   - Combines route results with memory context
   - Generates user-facing responses via GPT-4o-mini
   - Handles LLM failures with fallback responses

4. **Business Outputs Generation**
   - **Account Balance**: For balance inquiries
   - **Loan Eligibility**: For loan-related requests
   - **Exit Signals**: Conversation completion detection
   - Intent-based output customization

5. **Conversation Management**
   - Saves user input and AI response to contextual memory
   - Maintains dialog history for context continuity
   - Updates Redis with all processing results

6. **Context Synchronization**
   - Reloads latest Redis context before updates
   - Handles concurrent access and data consistency
   - Updates keys: `route`, `llm_answer`, business outputs, `latencyProcessing`

**Redis Keys Updated:** `route`, `llm_answer`, `account_balance`, `loan_eligibility`, `exit_signal`, `latencyProcessing`

---

### 🔊 **TTSAgent** - Speech Synthesis

**What it does internally:**

1. **Voice Configuration**
   - Loads emotion and gender from Redis context
   - Selects appropriate voice settings
   - Configures Google Text-to-Speech parameters

2. **Audio Generation**
   - **Google TTS Integration**: Primary synthesis engine
   - **ElevenLabs Support**: Alternative high-quality voices (commented)
   - Emotion-based voice modulation
   - Gender-appropriate voice selection

3. **File Management**
   - Generates unique audio files per session
   - Saves to project root with session-based naming
   - Returns file path for audio streaming

4. **Context Updates**
   - Saves audio path and processing latency to Redis
   - Updates keys: `audio_path`, `latencyTTS`
   - Maintains context for orchestrator retrieval

5. **Error Handling**
   - Fallback audio generation on synthesis failures
   - Standardized error responses with audio paths
   - Cleanup and resource management

**Redis Keys Updated:** `audio_path`, `latencyTTS`

---

### 🎵 **FillerTTSAgent** - Filler Audio Generation

**What it does internally:**

1. **Filler Text Selection**
   - Random selection from predefined phrases
   - Accepts custom filler text via input
   - Contextually appropriate messages

2. **TTS Integration**
   - Creates TTSAgent instance for synthesis
   - Processes filler text through standard TTS pipeline
   - Inherits voice configuration from session context

3. **Real-time Notifications**
   - Publishes filler status to orchestrator
   - Includes audio path for immediate playback
   - Enables seamless user experience during processing delays

**Use Cases:** Long processing delays, database queries, complex computations

---

## 🚨 Fallback & Error Handling

### **Redis Fallback Mechanism**
When Redis is unavailable, agents detect fallback context and:
- Return pre-configured fallback messages
- Generate fallback audio files
- Notify orchestrator of service degradation
- Maintain basic functionality without Redis dependency

### **Error Propagation**
- All errors logged with full context and stack traces
- Standardized error responses with appropriate HTTP status codes
- Fallback audio generation for user-facing errors
- Graceful degradation with minimal service interruption

---

## 📊 Performance Metrics

Each agent tracks and reports:
- **Processing Latency**: Individual agent processing time
- **Memory Usage**: Context and persistent memory access patterns
- **Error Rates**: Success/failure ratios with error categorization
- **Redis Performance**: Context save/load timing and pub/sub latency

---

## 🔧 Configuration Management

Agents support dynamic configuration via:
- **Environment Variables**: API keys and service endpoints
- **Config Files**: Feature toggles (emotion detection, gender detection)
- **Runtime Parameters**: Session-specific settings and preferences
- **Redis Context**: Dynamic behavior modification based on user state

